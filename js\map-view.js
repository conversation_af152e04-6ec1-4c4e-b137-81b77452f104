// 地图视图管理器
class MapView {
    constructor() {
        this.map = null;
        this.markers = [];
        this.clusterLayer = null;
        this.currentZoom = 10;
        this.selectedCompany = null;
        this.init();
    }

    init() {
        this.initMap();
        this.bindEvents();
    }

    // 初始化地图
    initMap() {
        // 检查高德地图API是否加载
        if (typeof AMap === 'undefined') {
            console.error('高德地图API未加载');
            this.showMapError();
            return;
        }

        try {
            // 创建地图实例
            this.map = new AMap.Map('amap-container', {
                zoom: this.currentZoom,
                center: [120.1693, 30.2467], // 杭州市中心
                mapStyle: 'amap://styles/normal',
                features: ['bg', 'road', 'building', 'point'],
                viewMode: '2D'
            });

            // 添加地图控件
            this.addMapControls();
            
            // 加载公司标点
            this.loadCompanyMarkers();
            
            // 监听地图缩放事件
            this.map.on('zoomend', () => {
                this.currentZoom = this.map.getZoom();
                this.updateClusterDisplay();
            });

            // 监听地图移动事件
            this.map.on('moveend', () => {
                this.updateVisibleCompanies();
            });

        } catch (error) {
            console.error('地图初始化失败:', error);
            this.showMapError();
        }
    }

    // 添加地图控件
    addMapControls() {
        // 缩放控件
        const zoomControl = new AMap.Scale({
            position: 'RB'
        });
        this.map.addControl(zoomControl);

        // 比例尺控件
        const scaleControl = new AMap.ToolBar({
            position: 'RT'
        });
        this.map.addControl(scaleControl);

        // 定位控件
        const geolocationControl = new AMap.Geolocation({
            position: 'RB',
            buttonPosition: 'RB',
            convert: true,
            showButton: true,
            showMarker: true,
            showCircle: true,
            panToLocation: true,
            zoomToAccuracy: true
        });
        this.map.addControl(geolocationControl);
    }

    // 加载公司标点
    loadCompanyMarkers() {
        const companies = dataManager.getFilteredCompanies();
        this.clearMarkers();

        companies.forEach(company => {
            this.createCompanyMarker(company);
        });

        this.createClusterLayer();
        this.updateClusterDisplay();
    }

    // 创建公司标记
    createCompanyMarker(company) {
        const marker = new AMap.Marker({
            position: company.coordinates,
            title: company.name,
            extData: company
        });

        // 自定义标记样式
        const markerContent = this.createMarkerContent(company);
        marker.setContent(markerContent);

        // 添加点击事件
        marker.on('click', () => {
            this.showCompanyDetail(company.id);
        });

        this.markers.push(marker);
        return marker;
    }

    // 创建标记内容
    createMarkerContent(company) {
        const categoryColors = {
            '化学': '#FF6B6B',
            '保険業': '#4ECDC4',
            '貿易業': '#45B7D1',
            '銀行業': '#96CEB4',
            'IT業': '#FFEAA7',
            '製造業': '#DDA0DD',
            '金融業': '#98D8C8',
            '電子業': '#F7DC6F'
        };

        const color = categoryColors[company.category] || '#667eea';
        
        return `
            <div class="custom-marker" style="background-color: ${color}">
                <div class="marker-content">
                    <span class="marker-icon">🏢</span>
                </div>
            </div>
        `;
    }

    // 创建聚合图层
    createClusterLayer() {
        if (!window.AMapUI) {
            console.warn('AMapUI未加载，无法使用聚合功能');
            this.addMarkersToMap();
            return;
        }

        AMapUI.loadUI(['misc/MarkerClusterer'], (MarkerClusterer) => {
            this.clusterLayer = new MarkerClusterer(this.map, this.markers, {
                gridSize: this.getGridSize(),
                maxZoom: 15,
                averageCenter: true,
                styles: this.getClusterStyles(),
                renderClusterMarker: this.renderClusterMarker.bind(this),
                renderMarker: this.renderMarker.bind(this)
            });

            // 监听聚合点击事件
            this.clusterLayer.on('click', (e) => {
                this.handleClusterClick(e);
            });
        });
    }

    // 根据缩放级别获取网格大小
    getGridSize() {
        const zoom = this.map.getZoom();
        if (zoom <= 8) return 120;
        if (zoom <= 10) return 100;
        if (zoom <= 12) return 80;
        if (zoom <= 14) return 60;
        return 40;
    }

    // 自定义聚合标记渲染
    renderClusterMarker(context) {
        const count = context.count;
        const marker = context.marker;

        // 根据聚合数量选择不同的样式
        let size, color, textSize;
        if (count < 10) {
            size = 40;
            color = '#667eea';
            textSize = 12;
        } else if (count < 50) {
            size = 50;
            color = '#4ECDC4';
            textSize = 14;
        } else {
            size = 60;
            color = '#FF6B6B';
            textSize = 16;
        }

        const content = `
            <div class="cluster-marker" style="
                width: ${size}px;
                height: ${size}px;
                background: ${color};
                border: 3px solid white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                cursor: pointer;
                transition: all 0.3s ease;
            ">
                <span style="
                    color: white;
                    font-size: ${textSize}px;
                    font-weight: bold;
                ">${count}</span>
            </div>
        `;

        marker.setContent(content);

        // 添加悬停效果
        marker.on('mouseover', () => {
            marker.setContent(content.replace('transform: scale(1)', 'transform: scale(1.1)'));
        });

        marker.on('mouseout', () => {
            marker.setContent(content);
        });
    }

    // 自定义单个标记渲染
    renderMarker(context) {
        const marker = context.marker;
        const company = marker.getExtData();

        if (company) {
            marker.setContent(this.createMarkerContent(company));
        }
    }

    // 处理聚合点击事件
    handleClusterClick(e) {
        const cluster = e.target;
        const markers = cluster.getMarkers();

        if (markers.length <= 10) {
            // 如果聚合点包含的标记较少，显示列表
            this.showClusterCompanies(markers);
        } else {
            // 如果聚合点包含的标记较多，放大地图
            const bounds = new AMap.LngLatBounds();
            markers.forEach(marker => {
                bounds.extend(marker.getPosition());
            });
            this.map.setBounds(bounds, false, [20, 20, 20, 20]);
        }
    }

    // 显示聚合点中的公司列表
    showClusterCompanies(markers) {
        const companies = markers.map(marker => marker.getExtData()).filter(Boolean);

        const listHtml = companies.map(company => `
            <div class="cluster-company-item" onclick="mapView.showCompanyDetail(${company.id})">
                <div class="company-name">${company.name}</div>
                <div class="company-category">${company.category}</div>
                <div class="company-address">${formatAddress(company.address)}</div>
            </div>
        `).join('');

        const detailContainer = document.getElementById('company-details');
        if (detailContainer) {
            detailContainer.innerHTML = `
                <div class="cluster-companies-list">
                    <div class="cluster-header">
                        <h3>该区域的公司 (${companies.length})</h3>
                        <button class="close-btn" onclick="mapView.hideCompanyDetail()">×</button>
                    </div>
                    <div class="cluster-companies">
                        ${listHtml}
                    </div>
                </div>
            `;
            detailContainer.parentElement.style.display = 'block';
        }
    }

    // 隐藏公司详情
    hideCompanyDetail() {
        const detailContainer = document.getElementById('company-details');
        if (detailContainer) {
            detailContainer.parentElement.style.display = 'none';
        }

        // 重置所有标记样式
        this.markers.forEach(marker => {
            const company = marker.getExtData();
            if (company) {
                marker.setContent(this.createMarkerContent(company));
            }
        });
    }

    // 获取聚合样式
    getClusterStyles() {
        return [
            {
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">
                        <circle cx="20" cy="20" r="18" fill="#667eea" stroke="#fff" stroke-width="2"/>
                        <text x="20" y="25" text-anchor="middle" fill="white" font-size="12" font-weight="bold">_TEXT_</text>
                    </svg>
                `),
                size: new AMap.Size(40, 40),
                offset: new AMap.Pixel(-20, -20),
                textColor: '#fff',
                textSize: 12
            },
            {
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 50 50">
                        <circle cx="25" cy="25" r="23" fill="#4ECDC4" stroke="#fff" stroke-width="2"/>
                        <text x="25" y="30" text-anchor="middle" fill="white" font-size="14" font-weight="bold">_TEXT_</text>
                    </svg>
                `),
                size: new AMap.Size(50, 50),
                offset: new AMap.Pixel(-25, -25),
                textColor: '#fff',
                textSize: 14
            },
            {
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60">
                        <circle cx="30" cy="30" r="28" fill="#FF6B6B" stroke="#fff" stroke-width="2"/>
                        <text x="30" y="35" text-anchor="middle" fill="white" font-size="16" font-weight="bold">_TEXT_</text>
                    </svg>
                `),
                size: new AMap.Size(60, 60),
                offset: new AMap.Pixel(-30, -30),
                textColor: '#fff',
                textSize: 16
            }
        ];
    }

    // 直接添加标记到地图（不使用聚合）
    addMarkersToMap() {
        this.markers.forEach(marker => {
            this.map.add(marker);
        });
    }

    // 更新聚合显示
    updateClusterDisplay() {
        if (this.clusterLayer) {
            this.clusterLayer.setMap(this.map);
        }
    }

    // 更新可见公司
    updateVisibleCompanies() {
        const bounds = this.map.getBounds();
        const visibleCompanies = dataManager.getFilteredCompanies().filter(company => {
            const [lng, lat] = company.coordinates;
            return bounds.contains(new AMap.LngLat(lng, lat));
        });

        // 更新结果计数
        this.updateMapResultCount(visibleCompanies.length);
    }

    // 更新地图结果计数
    updateMapResultCount(count) {
        const countElement = document.querySelector('.result-count-map');
        if (countElement) {
            countElement.textContent = `${count} 社の杭州商社をお見つけしました`;
        }
    }

    // 显示公司详情
    showCompanyDetail(companyId) {
        const company = dataManager.getCompanyById(companyId);
        if (!company) return;

        this.selectedCompany = company;
        
        // 移动地图到公司位置
        this.map.setCenter(company.coordinates);
        this.map.setZoom(Math.max(this.currentZoom, 15));

        // 在侧边栏显示详情
        this.renderCompanyDetail(company);

        // 高亮选中的标记
        this.highlightMarker(company);
    }

    // 渲染公司详情
    renderCompanyDetail(company) {
        const detailContainer = document.getElementById('company-details');
        if (!detailContainer) return;

        detailContainer.innerHTML = `
            <div class="company-detail-card">
                <div class="company-detail-header">
                    <h3>${company.name}</h3>
                    <span class="company-category">${company.category}</span>
                </div>
                <div class="company-detail-content">
                    <div class="detail-item">
                        <label>日本社名：</label>
                        <span>${company.nameJp}</span>
                    </div>
                    <div class="detail-item">
                        <label>住所：</label>
                        <span>${company.address}</span>
                    </div>
                    <div class="detail-item">
                        <label>電話：</label>
                        <span>${company.phone || 'N/A'}</span>
                    </div>
                    <div class="detail-item">
                        <label>ウェブサイト：</label>
                        <span>${company.website || 'N/A'}</span>
                    </div>
                    <div class="detail-item">
                        <label>説明：</label>
                        <span>${company.description || 'N/A'}</span>
                    </div>
                </div>
                <div class="company-detail-actions">
                    <button class="detail-btn" onclick="mapView.getDirections('${company.id}')">
                        🧭 ルート案内
                    </button>
                    <button class="detail-btn" onclick="mapView.shareLocation('${company.id}')">
                        📤 位置を共有
                    </button>
                </div>
            </div>
        `;

        // 显示侧边栏
        detailContainer.parentElement.style.display = 'block';
    }

    // 高亮标记
    highlightMarker(company) {
        // 重置所有标记
        this.markers.forEach(marker => {
            const markerCompany = marker.getExtData();
            if (markerCompany.id === company.id) {
                // 高亮选中的标记
                marker.setContent(this.createHighlightedMarkerContent(company));
            } else {
                // 恢复其他标记
                marker.setContent(this.createMarkerContent(markerCompany));
            }
        });
    }

    // 创建高亮标记内容
    createHighlightedMarkerContent(company) {
        return `
            <div class="custom-marker highlighted" style="background-color: #FF6B6B; transform: scale(1.2);">
                <div class="marker-content">
                    <span class="marker-icon">🏢</span>
                </div>
            </div>
        `;
    }

    // 清除所有标记
    clearMarkers() {
        if (this.clusterLayer) {
            this.clusterLayer.setMap(null);
            this.clusterLayer = null;
        }

        this.markers.forEach(marker => {
            this.map.remove(marker);
        });
        this.markers = [];
    }

    // 绑定事件
    bindEvents() {
        // 地图搜索
        const mapSearchInput = document.getElementById('map-area-search');
        if (mapSearchInput) {
            mapSearchInput.addEventListener('input', debounce((e) => {
                this.handleMapSearch(e.target.value);
            }, 300));
        }

        // 返回区域搜索按钮
        const returnBtn = document.querySelector('.area-return-btn');
        if (returnBtn) {
            returnBtn.addEventListener('click', () => {
                this.returnToAreaView();
            });
        }

        // 地图控制按钮
        const zoomInBtn = document.getElementById('zoom-in');
        const zoomOutBtn = document.getElementById('zoom-out');
        const locateBtn = document.getElementById('locate-me');

        if (zoomInBtn) {
            zoomInBtn.addEventListener('click', () => {
                this.map.zoomIn();
            });
        }

        if (zoomOutBtn) {
            zoomOutBtn.addEventListener('click', () => {
                this.map.zoomOut();
            });
        }

        if (locateBtn) {
            locateBtn.addEventListener('click', () => {
                this.locateUser();
            });
        }
    }

    // 处理地图搜索
    handleMapSearch(keyword) {
        dataManager.searchCompanies(keyword);
        this.loadCompanyMarkers();
        this.updateMapResultCount(dataManager.getTotalCount());
    }

    // 返回区域视图
    returnToAreaView() {
        // 切换到列表视图
        document.querySelector('.nav-tab[data-view="list"]').click();
    }

    // 定位用户
    locateUser() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const { latitude, longitude } = position.coords;
                    this.map.setCenter([longitude, latitude]);
                    this.map.setZoom(15);
                    
                    // 添加用户位置标记
                    const userMarker = new AMap.Marker({
                        position: [longitude, latitude],
                        icon: new AMap.Icon({
                            image: 'data:image/svg+xml;base64,' + btoa(`
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
                                    <circle cx="10" cy="10" r="8" fill="#4285F4" stroke="#fff" stroke-width="2"/>
                                </svg>
                            `),
                            size: new AMap.Size(20, 20),
                            imageOffset: new AMap.Pixel(-10, -10)
                        })
                    });
                    
                    this.map.add(userMarker);
                },
                (error) => {
                    console.error('定位失败:', error);
                    Notification.error('定位失败，请检查位置权限设置');
                }
            );
        } else {
            Notification.error('浏览器不支持定位功能');
        }
    }

    // 获取路线导航
    getDirections(companyId) {
        const company = dataManager.getCompanyById(companyId);
        if (!company) return;

        // 这里可以集成高德地图的路线规划功能
        const url = `https://uri.amap.com/navigation?to=${company.coordinates[0]},${company.coordinates[1]}&toname=${encodeURIComponent(company.name)}`;
        window.open(url, '_blank');
    }

    // 分享位置
    shareLocation(companyId) {
        const company = dataManager.getCompanyById(companyId);
        if (!company) return;

        const shareText = `${company.name}\n地址：${company.address}\n位置：${company.coordinates.join(',')}`;
        
        if (navigator.share) {
            navigator.share({
                title: company.name,
                text: shareText,
                url: window.location.href
            });
        } else {
            // 复制到剪贴板
            navigator.clipboard.writeText(shareText).then(() => {
                Notification.success('位置信息已复制到剪贴板');
            });
        }
    }

    // 显示地图错误
    showMapError() {
        const container = document.getElementById('amap-container');
        if (container) {
            container.innerHTML = `
                <div class="map-error">
                    <div class="error-content">
                        <h3>地图加载失败</h3>
                        <p>请检查网络连接或稍后重试</p>
                        <button onclick="location.reload()" class="retry-btn">重新加载</button>
                    </div>
                </div>
            `;
        }
    }

    // 刷新地图数据
    refresh() {
        this.loadCompanyMarkers();
        this.updateMapResultCount(dataManager.getTotalCount());
    }

    // 销毁地图
    destroy() {
        if (this.map) {
            this.map.destroy();
            this.map = null;
        }
        this.markers = [];
        this.clusterLayer = null;
    }
}

// 创建地图视图实例
let mapView;
