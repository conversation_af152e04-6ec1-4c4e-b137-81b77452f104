// 应用程序配置文件
const CONFIG = {
    // 高德地图配置
    AMAP: {
        // 请替换为您的高德地图API密钥，或使用配置向导设置
        KEY: localStorage.getItem('amap_api_key') || 'b9c03cb6a5d05c83348d2c9c5636a3c9',
        // 地图默认配置
        DEFAULT_CENTER: [120.1693, 30.2467], // 杭州市中心
        DEFAULT_ZOOM: 10,
        MAP_STYLE: 'amap://styles/normal',
        // 聚合配置
        CLUSTER: {
            GRID_SIZE: {
                ZOOM_8: 120,
                ZOOM_10: 100,
                ZOOM_12: 80,
                ZOOM_14: 60,
                DEFAULT: 40
            },
            MAX_ZOOM: 15,
            STYLES: {
                SMALL: {
                    SIZE: 40,
                    COLOR: '#667eea',
                    TEXT_SIZE: 12
                },
                MEDIUM: {
                    SIZE: 50,
                    COLOR: '#4ECDC4',
                    TEXT_SIZE: 14
                },
                LARGE: {
                    SIZE: 60,
                    COLOR: '#FF6B6B',
                    TEXT_SIZE: 16
                }
            }
        }
    },
    
    // 分页配置
    PAGINATION: {
        PAGE_SIZE: 10,
        MAX_VISIBLE_PAGES: 5
    },
    
    // 搜索配置
    SEARCH: {
        DEBOUNCE_DELAY: 300,
        MIN_SEARCH_LENGTH: 1
    },
    
    // 通知配置
    NOTIFICATION: {
        DEFAULT_DURATION: 3000,
        ERROR_DURATION: 5000,
        SUCCESS_DURATION: 2000
    },
    
    // 动画配置
    ANIMATION: {
        TRANSITION_DURATION: 300,
        MAP_INIT_DELAY: 100
    },
    
    // 本地存储键名
    STORAGE_KEYS: {
        USER_PREFERENCES: 'company_search_preferences',
        LAST_SEARCH: 'company_search_last_search',
        FAVORITE_COMPANIES: 'company_search_favorites'
    },
    
    // API端点（如果有后端API）
    API: {
        BASE_URL: '',
        ENDPOINTS: {
            COMPANIES: '/api/companies',
            SEARCH: '/api/companies/search',
            DETAILS: '/api/companies/:id'
        }
    },
    
    // 地图标记颜色配置
    MARKER_COLORS: {
        '化学': '#FF6B6B',
        '保険業': '#4ECDC4',
        '貿易業': '#45B7D1',
        '銀行業': '#96CEB4',
        'IT業': '#FFEAA7',
        '製造業': '#DDA0DD',
        '金融業': '#98D8C8',
        '電子業': '#F7DC6F',
        'DEFAULT': '#667eea'
    },
    
    // 响应式断点
    BREAKPOINTS: {
        MOBILE: 768,
        TABLET: 1024,
        DESKTOP: 1200
    },
    
    // 功能开关
    FEATURES: {
        ENABLE_GEOLOCATION: true,
        ENABLE_CLUSTERING: true,
        ENABLE_ROUTE_PLANNING: true,
        ENABLE_SHARING: true,
        ENABLE_FAVORITES: false,
        ENABLE_ANALYTICS: false
    },
    
    // 错误消息
    ERROR_MESSAGES: {
        MAP_LOAD_FAILED: '地图加载失败，请检查网络连接',
        GEOLOCATION_FAILED: '定位失败，请检查位置权限设置',
        SEARCH_FAILED: '搜索失败，请重试',
        DATA_LOAD_FAILED: '数据加载失败，请刷新页面',
        BROWSER_NOT_SUPPORTED: '浏览器不支持此功能'
    },
    
    // 成功消息
    SUCCESS_MESSAGES: {
        LOCATION_COPIED: '位置信息已复制到剪贴板',
        SEARCH_COMPLETED: '搜索完成',
        DATA_UPDATED: '数据已更新'
    }
};

// 环境检测和配置调整
(function() {
    'use strict';
    
    // 检测是否为开发环境
    const isDevelopment = window.location.hostname === 'localhost' || 
                         window.location.hostname === '127.0.0.1' ||
                         window.location.hostname.includes('dev');
    
    if (isDevelopment) {
        // 开发环境配置
        CONFIG.DEBUG = true;
        CONFIG.API.BASE_URL = 'http://localhost:3000';
        console.log('运行在开发环境');
    } else {
        // 生产环境配置
        CONFIG.DEBUG = false;
        CONFIG.API.BASE_URL = '';
        console.log('运行在生产环境');
    }
    
    // 检测设备类型
    const userAgent = navigator.userAgent.toLowerCase();
    CONFIG.DEVICE = {
        IS_MOBILE: /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent),
        IS_TABLET: /ipad|android(?!.*mobile)/i.test(userAgent),
        IS_IOS: /iphone|ipad|ipod/i.test(userAgent),
        IS_ANDROID: /android/i.test(userAgent)
    };
    
    CONFIG.DEVICE.IS_DESKTOP = !CONFIG.DEVICE.IS_MOBILE && !CONFIG.DEVICE.IS_TABLET;
    
    // 根据设备类型调整配置
    if (CONFIG.DEVICE.IS_MOBILE) {
        CONFIG.PAGINATION.PAGE_SIZE = 5;
        CONFIG.AMAP.CLUSTER.GRID_SIZE.DEFAULT = 60;
    }
    
    // 检测浏览器支持的功能
    CONFIG.BROWSER_SUPPORT = {
        GEOLOCATION: 'geolocation' in navigator,
        LOCAL_STORAGE: typeof Storage !== 'undefined',
        HISTORY_API: !!(window.history && window.history.pushState),
        WEB_SHARE: 'share' in navigator,
        CLIPBOARD: 'clipboard' in navigator
    };
    
    // 根据浏览器支持情况调整功能开关
    if (!CONFIG.BROWSER_SUPPORT.GEOLOCATION) {
        CONFIG.FEATURES.ENABLE_GEOLOCATION = false;
    }
    
    if (!CONFIG.BROWSER_SUPPORT.WEB_SHARE && !CONFIG.BROWSER_SUPPORT.CLIPBOARD) {
        CONFIG.FEATURES.ENABLE_SHARING = false;
    }
    
})();

// 配置验证
function validateConfig() {
    const errors = [];
    
    // 检查必需的配置项
    if (!CONFIG.AMAP.KEY || CONFIG.AMAP.KEY === 'YOUR_AMAP_KEY_HERE') {
        errors.push('高德地图API密钥未配置');
    }
    
    if (!Array.isArray(CONFIG.AMAP.DEFAULT_CENTER) || CONFIG.AMAP.DEFAULT_CENTER.length !== 2) {
        errors.push('地图默认中心点配置错误');
    }
    
    if (typeof CONFIG.AMAP.DEFAULT_ZOOM !== 'number' || CONFIG.AMAP.DEFAULT_ZOOM < 1 || CONFIG.AMAP.DEFAULT_ZOOM > 20) {
        errors.push('地图默认缩放级别配置错误');
    }
    
    if (errors.length > 0) {
        console.error('配置验证失败:', errors);
        return false;
    }
    
    return true;
}

// 获取配置值的辅助函数
function getConfig(path, defaultValue = null) {
    const keys = path.split('.');
    let value = CONFIG;
    
    for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
            value = value[key];
        } else {
            return defaultValue;
        }
    }
    
    return value;
}

// 设置配置值的辅助函数
function setConfig(path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    let target = CONFIG;
    
    for (const key of keys) {
        if (!(key in target) || typeof target[key] !== 'object') {
            target[key] = {};
        }
        target = target[key];
    }
    
    target[lastKey] = value;
}

// 导出配置对象和辅助函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        CONFIG,
        validateConfig,
        getConfig,
        setConfig
    };
} else {
    // 浏览器环境下添加到全局作用域
    window.CONFIG = CONFIG;
    window.validateConfig = validateConfig;
    window.getConfig = getConfig;
    window.setConfig = setConfig;
}
