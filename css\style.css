/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-tabs {
    display: flex;
    gap: 1rem;
}

.nav-tab {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.nav-tab:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.nav-tab.active {
    background: white;
    color: #667eea;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    width: 100%;
}

.view-container {
    display: none;
}

.view-container.active {
    display: block;
}

/* 搜索区域样式 */
.search-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.search-header h2 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #2c3e50;
    font-size: 1.3rem;
}

.search-info {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.search-form {
    space-y: 1.5rem;
}

.search-row {
    margin-bottom: 1.5rem;
}

.search-group {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-group label {
    font-weight: 500;
    color: #34495e;
    min-width: 80px;
}

.search-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-btn:hover {
    background: #5a6fd8;
}

/* 筛选标签样式 */
.filter-tags {
    margin-top: 1.5rem;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.filter-label {
    font-weight: 500;
    color: #34495e;
    min-width: 60px;
}

.filter-tag {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    color: #495057;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.85rem;
}

.filter-tag:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.filter-tag.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* 结果区域样式 */
.results-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.results-header h3 {
    color: #2c3e50;
    font-size: 1.1rem;
}

.result-count {
    color: #667eea;
    font-weight: 600;
}

.view-options {
    color: #7f8c8d;
    font-size: 0.9rem;
    cursor: pointer;
}

.view-options:hover {
    color: #667eea;
}

/* 公司列表样式 */
.company-list {
    space-y: 1rem;
}

.company-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    background: #fafbfc;
}

.company-item:hover {
    border-color: #667eea;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.company-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.company-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.company-category {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.company-details {
    color: #666;
    line-height: 1.6;
}

.company-address {
    margin-bottom: 0.5rem;
}

.map-link {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.map-link:hover {
    text-decoration: underline;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

.page-btn, .page-num {
    background: white;
    border: 1px solid #e9ecef;
    color: #495057;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.page-btn:hover:not(:disabled), .page-num:hover {
    background: #f8f9fa;
    border-color: #667eea;
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-num.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.page-numbers {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* 地图视图样式 */
.map-search-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.map-search-form {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

.map-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.result-count-map {
    color: #667eea;
    font-weight: 500;
    font-size: 0.9rem;
}

.area-return-btn {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    color: #495057;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.area-return-btn:hover {
    background: #e9ecef;
    color: #667eea;
}

/* 地图容器样式 */
.map-container {
    position: relative;
    height: 70vh;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

#amap-container {
    width: 100%;
    height: 100%;
}

.map-sidebar {
    position: absolute;
    top: 1rem;
    left: 1rem;
    width: 300px;
    max-height: calc(100% - 2rem);
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    overflow-y: auto;
    z-index: 1000;
}

.map-controls-bottom {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    z-index: 1000;
}

.map-control-btn {
    background: white;
    border: 1px solid #e9ecef;
    color: #495057;
    width: 40px;
    height: 40px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.map-control-btn:hover {
    background: #f8f9fa;
    border-color: #667eea;
    color: #667eea;
}

/* 响应式设计 */

/* 平板设备 */
@media (max-width: 1024px) {
    .main-content {
        padding: 1.5rem;
    }

    .search-section, .results-section {
        padding: 1.5rem;
    }

    .map-sidebar {
        width: 280px;
    }

    .company-list {
        grid-template-columns: 1fr;
    }
}

/* 移动设备 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
        padding: 0 1rem;
    }

    .logo {
        font-size: 1.3rem;
    }

    .nav-tabs {
        width: 100%;
        justify-content: center;
    }

    .nav-tab {
        flex: 1;
        max-width: 150px;
        padding: 0.6rem 1rem;
        font-size: 0.85rem;
    }

    .main-content {
        padding: 1rem;
    }

    .search-section, .results-section, .map-search-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .search-header {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .search-header h2 {
        font-size: 1.1rem;
    }

    .search-group {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }

    .search-group label {
        min-width: auto;
        font-size: 0.9rem;
    }

    .search-input {
        padding: 0.8rem;
        font-size: 16px; /* 防止iOS缩放 */
    }

    .search-btn {
        padding: 0.8rem;
        width: 100%;
        margin-top: 0.5rem;
    }

    .filter-group {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }

    .filter-label {
        min-width: auto;
        margin-bottom: 0.5rem;
    }

    .filter-tag {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
        text-align: center;
    }

    .results-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .results-header h3 {
        font-size: 1rem;
    }

    .company-item {
        padding: 1rem;
        margin-bottom: 0.75rem;
    }

    .company-header {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
    }

    .company-name {
        font-size: 1rem;
    }

    .company-category {
        align-self: flex-start;
    }

    .company-details {
        font-size: 0.85rem;
    }

    .map-search-form {
        flex-direction: column;
        gap: 1rem;
    }

    .map-controls {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
    }

    .result-count-map {
        text-align: center;
    }

    .area-return-btn {
        width: 100%;
        justify-content: center;
    }

    .map-container {
        height: 60vh;
        margin-bottom: 1rem;
    }

    .map-sidebar {
        position: static;
        width: 100%;
        max-height: none;
        margin-bottom: 1rem;
        border-radius: 8px;
    }

    .map-controls-bottom {
        bottom: 0.5rem;
        right: 0.5rem;
    }

    .map-control-btn {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .pagination {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .page-btn, .page-num {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }

    .page-numbers {
        gap: 0.125rem;
    }

    /* 公司详情卡片移动端优化 */
    .company-detail-card {
        padding: 1rem;
    }

    .company-detail-header h3 {
        font-size: 1rem;
    }

    .detail-item {
        margin-bottom: 0.5rem;
    }

    .detail-item label {
        font-size: 0.8rem;
    }

    .detail-item span {
        font-size: 0.85rem;
    }

    .company-detail-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .detail-btn {
        width: 100%;
        justify-content: center;
        padding: 0.75rem;
    }

    /* 聚合公司列表移动端优化 */
    .cluster-companies-list {
        padding: 1rem;
        max-height: 300px;
    }

    .cluster-company-item {
        padding: 0.75rem;
    }

    .cluster-company-item .company-name {
        font-size: 0.85rem;
    }

    .cluster-company-item .company-address {
        font-size: 0.75rem;
    }
}

/* 小屏幕移动设备 */
@media (max-width: 480px) {
    .header-content {
        padding: 0 0.5rem;
    }

    .main-content {
        padding: 0.5rem;
    }

    .search-section, .results-section, .map-search-section {
        padding: 0.75rem;
        border-radius: 8px;
    }

    .nav-tab {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }

    .company-item {
        padding: 0.75rem;
    }

    .map-container {
        height: 50vh;
    }

    .filter-tag {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }

    .pagination {
        justify-content: center;
    }

    .page-numbers span {
        display: none; /* 隐藏省略号在小屏幕上 */
    }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
    .map-container {
        height: 70vh;
    }

    .header {
        padding: 0.5rem 0;
    }

    .main-content {
        padding: 0.5rem;
    }
}

/* 高分辨率屏幕优化 */
@media (min-width: 1200px) {
    .main-content {
        max-width: 1400px;
        padding: 2.5rem;
    }

    .search-section, .results-section {
        padding: 2.5rem;
    }

    .company-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
        gap: 1.5rem;
    }

    .map-sidebar {
        width: 350px;
    }
}

/* 打印样式 */
@media print {
    .header, .nav-tabs, .search-section, .pagination, .map-controls-bottom {
        display: none;
    }

    .main-content {
        padding: 0;
        max-width: none;
    }

    .company-item {
        break-inside: avoid;
        border: 1px solid #ccc;
        margin-bottom: 1rem;
    }

    .map-container {
        height: 400px;
    }
}

/* 图标样式 */
.icon {
    display: inline-block;
    font-style: normal;
}

/* 自定义地图标记样式 */
.custom-marker {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    border: 2px solid white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.custom-marker:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.4);
}

.custom-marker.highlighted {
    animation: pulse 2s infinite;
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
    }
}

.marker-content {
    color: white;
    font-size: 14px;
    font-weight: bold;
}

.marker-icon {
    font-size: 12px;
}

/* 公司详情卡片样式 */
.company-detail-card {
    padding: 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.company-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.company-detail-header h3 {
    color: #2c3e50;
    font-size: 1.1rem;
    margin: 0;
    flex: 1;
    margin-right: 1rem;
}

.company-detail-content {
    margin-bottom: 1.5rem;
}

.detail-item {
    margin-bottom: 0.75rem;
    display: flex;
    flex-direction: column;
}

.detail-item label {
    font-weight: 500;
    color: #666;
    font-size: 0.85rem;
    margin-bottom: 0.25rem;
}

.detail-item span {
    color: #333;
    font-size: 0.9rem;
    line-height: 1.4;
}

.company-detail-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.detail-btn {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    color: #495057;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.detail-btn:hover {
    background: #e9ecef;
    color: #667eea;
    border-color: #667eea;
}

/* 地图错误样式 */
.map-error {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #f8f9fa;
}

.error-content {
    text-align: center;
    color: #666;
}

.error-content h3 {
    color: #333;
    margin-bottom: 0.5rem;
}

.error-content p {
    margin-bottom: 1rem;
}

.retry-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.retry-btn:hover {
    background: #5a6fd8;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.notification.show {
    opacity: 1;
    transform: translateX(0);
}

.notification-content {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-left: 4px solid #667eea;
}

.notification-success .notification-content {
    border-left-color: #28a745;
}

.notification-error .notification-content {
    border-left-color: #dc3545;
}

.notification-warning .notification-content {
    border-left-color: #ffc107;
}

.notification-message {
    flex: 1;
    color: #333;
    font-size: 0.9rem;
}

.notification-close {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 1.2rem;
    margin-left: 1rem;
}

.notification-close:hover {
    color: #666;
}

/* 加载覆盖层样式 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: #666;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e9ecef;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.loading-message {
    font-size: 0.9rem;
}

/* 聚合标记样式 */
.cluster-marker {
    position: relative;
}

.cluster-marker:hover {
    transform: scale(1.1) !important;
}

/* 聚合公司列表样式 */
.cluster-companies-list {
    padding: 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    max-height: 400px;
    overflow-y: auto;
}

.cluster-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.cluster-header h3 {
    color: #2c3e50;
    font-size: 1.1rem;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 1.5rem;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #f8f9fa;
    color: #666;
}

.cluster-companies {
    space-y: 0.5rem;
}

.cluster-company-item {
    padding: 0.75rem;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
}

.cluster-company-item:hover {
    border-color: #667eea;
    background: #f8f9fa;
    transform: translateY(-1px);
}

.cluster-company-item .company-name {
    font-weight: 500;
    color: #2c3e50;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.cluster-company-item .company-category {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.125rem 0.5rem;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 500;
    display: inline-block;
    margin-bottom: 0.25rem;
}

.cluster-company-item .company-address {
    color: #666;
    font-size: 0.8rem;
    line-height: 1.3;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.company-item {
    animation: fadeIn 0.5s ease-out;
}

/* 加载状态 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    color: #7f8c8d;
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
