<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公司检索系统</title>
    <link rel="stylesheet" href="css/style.css">
    <!-- 高德地图API -->
    <script type="text/javascript">
        // 动态加载高德地图API
        (function() {
            // 从localStorage获取API密钥，如果没有则使用默认值
            const apiKey = localStorage.getItem('amap_api_key') || 'YOUR_AMAP_KEY';

            if (apiKey === 'YOUR_AMAP_KEY' || !apiKey) {
                console.warn('高德地图API密钥未配置，请访问 setup.html 进行配置');
            }

            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = `https://webapi.amap.com/maps?v=1.4.15&key=${apiKey}`;
            script.async = true;
            script.onerror = function() {
                console.error('高德地图API加载失败，请检查API密钥配置');
                // 显示配置提示
                if (window.app) {
                    window.app.showError('地图API加载失败，请检查API密钥配置。<a href="setup.html">点击这里配置</a>');
                }
            };
            document.head.appendChild(script);

            // 加载UI组件库
            const uiScript = document.createElement('script');
            uiScript.src = 'https://webapi.amap.com/ui/1.1/main.js?v=1.1.10';
            uiScript.async = true;
            document.head.appendChild(uiScript);
        })();
    </script>
</head>
<body>
    <div class="container">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <h1 class="logo">
                    <span class="icon">🏢</span>
                    公司检索系统
                </h1>
                <nav class="nav-tabs">
                    <button class="nav-tab active" data-view="list">
                        <span class="icon">📋</span>
                        列表检索
                    </button>
                    <button class="nav-tab" data-view="map">
                        <span class="icon">🗺️</span>
                        地图检索
                    </button>
                </nav>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 列表检索页面 -->
            <div id="list-view" class="view-container active">
                <!-- 搜索区域 -->
                <div class="search-section">
                    <div class="search-header">
                        <h2>
                            <span class="icon">🔍</span>
                            公司检索
                        </h2>
                        <div class="search-info">
                            <span>地图检索です</span>
                        </div>
                    </div>
                    
                    <!-- 搜索表单 -->
                    <div class="search-form">
                        <div class="search-row">
                            <div class="search-group">
                                <label>エリア検索</label>
                                <input type="text" id="area-search" placeholder="都市、地域、社名を入力してお探しください" class="search-input">
                                <button class="search-btn">🔍</button>
                            </div>
                        </div>
                        
                        <div class="filter-tags">
                            <div class="filter-group">
                                <span class="filter-label">都市別</span>
                                <button class="filter-tag">問い合わせ</button>
                                <button class="filter-tag active">杭州です</button>
                                <button class="filter-tag">青島です</button>
                            </div>
                            
                            <div class="filter-group">
                                <span class="filter-label">区域別</span>
                                <button class="filter-tag">問い合わせ</button>
                                <button class="filter-tag">アップタウン</button>
                                <button class="filter-tag">明珠地域です</button>
                                <button class="filter-tag">滨水地方です</button>
                                <button class="filter-tag">浜江区です</button>
                                <button class="filter-tag">富山区です</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 结果区域 -->
                <div class="results-section">
                    <div class="results-header">
                        <h3>
                            <span class="result-count">123</span>
                            社の杭州商社をお見つけしました
                        </h3>
                        <div class="view-options">
                            <span>クリア条件です</span>
                        </div>
                    </div>
                    
                    <!-- 公司列表 -->
                    <div class="company-list" id="company-list">
                        <!-- 公司项目将通过JavaScript动态生成 -->
                    </div>
                    
                    <!-- 分页 -->
                    <div class="pagination">
                        <button class="page-btn prev" disabled>‹ 上一页</button>
                        <div class="page-numbers">
                            <button class="page-num active">1</button>
                            <button class="page-num">2</button>
                            <button class="page-num">3</button>
                            <span>...</span>
                            <button class="page-num">10</button>
                        </div>
                        <button class="page-btn next">下一页 ›</button>
                    </div>
                </div>
            </div>

            <!-- 地图检索页面 -->
            <div id="map-view" class="view-container">
                <!-- 地图搜索区域 -->
                <div class="map-search-section">
                    <div class="map-search-form">
                        <div class="search-group">
                            <span class="icon">🔍</span>
                            <input type="text" id="map-area-search" placeholder="都市、地域、社名を入力してお探しください" class="search-input">
                        </div>
                        <div class="map-controls">
                            <span class="result-count-map">123 社の杭州商社をお見つけしました</span>
                            <button class="area-return-btn">
                                <span class="icon">↩️</span>
                                エリア検索に戻ります
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 地图容器 -->
                <div class="map-container">
                    <div id="amap-container"></div>
                    
                    <!-- 地图侧边栏 -->
                    <div class="map-sidebar">
                        <div class="company-details" id="company-details">
                            <!-- 选中的公司详情将在这里显示 -->
                        </div>
                    </div>
                </div>

                <!-- 高德地图控件 -->
                <div class="map-controls-bottom">
                    <button class="map-control-btn" id="zoom-in">+</button>
                    <button class="map-control-btn" id="zoom-out">-</button>
                    <button class="map-control-btn" id="locate-me">📍</button>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript文件 -->
    <script src="js/config.js"></script>
    <script src="js/data.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/list-view.js"></script>
    <script src="js/map-view.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
