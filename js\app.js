// 主应用程序
class App {
    constructor() {
        this.currentView = 'list';
        this.isInitialized = false;
        this.init();
    }

    init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.initializeApp();
            });
        } else {
            this.initializeApp();
        }
    }

    initializeApp() {
        try {
            // 初始化视图管理器
            this.initViewManagers();
            
            // 绑定导航事件
            this.bindNavigationEvents();
            
            // 初始化默认视图
            this.showView('list');
            
            // 监听浏览器历史记录变化
            this.bindHistoryEvents();
            
            // 从URL参数恢复状态
            this.restoreStateFromURL();
            
            this.isInitialized = true;
            console.log('应用程序初始化完成');
            
        } catch (error) {
            console.error('应用程序初始化失败:', error);
            this.showError('应用程序初始化失败，请刷新页面重试');
        }
    }

    // 初始化视图管理器
    initViewManagers() {
        // 初始化列表视图
        listView = new ListView();
        
        // 延迟初始化地图视图（避免在隐藏状态下初始化地图）
        window.mapView = null;
    }

    // 绑定导航事件
    bindNavigationEvents() {
        const navTabs = document.querySelectorAll('.nav-tab');
        
        navTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const viewName = tab.getAttribute('data-view');
                this.showView(viewName);
            });
        });
    }

    // 显示指定视图
    showView(viewName) {
        if (this.currentView === viewName) return;

        // 更新导航标签状态
        this.updateNavigationTabs(viewName);
        
        // 隐藏当前视图
        this.hideCurrentView();
        
        // 显示新视图
        this.showNewView(viewName);
        
        // 更新当前视图
        this.currentView = viewName;
        
        // 更新URL
        this.updateURL(viewName);
        
        // 触发视图切换事件
        eventBus.emit('viewChanged', { from: this.currentView, to: viewName });
    }

    // 更新导航标签状态
    updateNavigationTabs(activeView) {
        const navTabs = document.querySelectorAll('.nav-tab');
        
        navTabs.forEach(tab => {
            const viewName = tab.getAttribute('data-view');
            if (viewName === activeView) {
                tab.classList.add('active');
            } else {
                tab.classList.remove('active');
            }
        });
    }

    // 隐藏当前视图
    hideCurrentView() {
        const currentViewElement = document.getElementById(`${this.currentView}-view`);
        if (currentViewElement) {
            currentViewElement.classList.remove('active');
        }
    }

    // 显示新视图
    showNewView(viewName) {
        const newViewElement = document.getElementById(`${viewName}-view`);
        if (!newViewElement) {
            console.error(`视图元素未找到: ${viewName}-view`);
            return;
        }

        newViewElement.classList.add('active');

        // 如果是地图视图且尚未初始化，则初始化地图
        if (viewName === 'map' && !window.mapView) {
            this.initializeMapView();
        }

        // 刷新视图数据
        this.refreshViewData(viewName);
    }

    // 初始化地图视图
    initializeMapView() {
        try {
            // 延迟初始化以确保容器可见
            setTimeout(() => {
                window.mapView = new MapView();
                console.log('地图视图初始化完成');
            }, 100);
        } catch (error) {
            console.error('地图视图初始化失败:', error);
            this.showError('地图加载失败，请检查网络连接');
        }
    }

    // 刷新视图数据
    refreshViewData(viewName) {
        try {
            switch (viewName) {
                case 'list':
                    if (listView) {
                        listView.refresh();
                    }
                    break;
                case 'map':
                    if (window.mapView) {
                        window.mapView.refresh();
                    }
                    break;
            }
        } catch (error) {
            console.error(`刷新${viewName}视图数据失败:`, error);
        }
    }

    // 绑定浏览器历史记录事件
    bindHistoryEvents() {
        window.addEventListener('popstate', (e) => {
            const state = e.state;
            if (state && state.view) {
                this.showView(state.view);
            } else {
                this.restoreStateFromURL();
            }
        });
    }

    // 从URL参数恢复状态
    restoreStateFromURL() {
        const urlParams = URLParams.getAll();
        
        // 恢复视图
        const view = urlParams.view || 'list';
        this.showView(view);
        
        // 恢复搜索条件
        if (urlParams.keyword) {
            const searchInput = document.getElementById('area-search');
            if (searchInput) {
                searchInput.value = urlParams.keyword;
                dataManager.searchCompanies(urlParams.keyword);
            }
        }
        
        // 恢复筛选条件
        if (urlParams.city) {
            dataManager.filterByCity(urlParams.city);
            this.activateFilterTag('city', urlParams.city);
        }
        
        if (urlParams.district) {
            dataManager.filterByDistrict(urlParams.district);
            this.activateFilterTag('district', urlParams.district);
        }
        
        // 刷新视图
        this.refreshViewData(view);
    }

    // 激活筛选标签
    activateFilterTag(type, value) {
        const filterTags = document.querySelectorAll('.filter-tag');
        filterTags.forEach(tag => {
            const tagValue = this.convertToChineseValue(type, tag.textContent.trim());
            if (tagValue === value) {
                tag.classList.add('active');
            }
        });
    }

    // 转换日文标签为中文值（与ListView中的方法保持一致）
    convertToChineseValue(type, japaneseValue) {
        const mappings = {
            city: {
                '杭州です': '杭州',
                '青島です': '青島',
                '上海です': '上海',
                '北京です': '北京',
                '深圳です': '深圳'
            },
            district: {
                'アップタウン': '上城区',
                '明珠地域です': '明珠地区',
                '滨水地方です': '滨江区',
                '浜江区です': '滨江区',
                '富山区です': '萧山区'
            }
        };

        return mappings[type]?.[japaneseValue] || japaneseValue;
    }

    // 更新URL
    updateURL(viewName) {
        const url = new URL(window.location);
        url.searchParams.set('view', viewName);
        
        // 添加当前筛选条件到URL
        const filters = dataManager.currentFilters;
        if (filters.keyword) {
            url.searchParams.set('keyword', filters.keyword);
        } else {
            url.searchParams.delete('keyword');
        }
        
        if (filters.city) {
            url.searchParams.set('city', filters.city);
        } else {
            url.searchParams.delete('city');
        }
        
        if (filters.district) {
            url.searchParams.set('district', filters.district);
        } else {
            url.searchParams.delete('district');
        }
        
        // 更新浏览器历史记录
        window.history.pushState({ view: viewName }, '', url);
    }

    // 显示错误信息
    showError(message) {
        Notification.error(message);
    }

    // 显示成功信息
    showSuccess(message) {
        Notification.success(message);
    }

    // 显示信息
    showInfo(message) {
        Notification.info(message);
    }

    // 获取当前视图
    getCurrentView() {
        return this.currentView;
    }

    // 检查是否已初始化
    isReady() {
        return this.isInitialized;
    }

    // 销毁应用程序
    destroy() {
        // 销毁地图视图
        if (window.mapView) {
            window.mapView.destroy();
            window.mapView = null;
        }
        
        // 清理事件监听器
        eventBus.off('viewChanged');
        
        this.isInitialized = false;
        console.log('应用程序已销毁');
    }
}

// 全局应用程序实例
let app;

// 应用程序入口点
(function() {
    'use strict';
    
    // 创建应用程序实例
    app = new App();
    
    // 全局错误处理
    window.addEventListener('error', (e) => {
        console.error('全局错误:', e.error);
        if (app) {
            app.showError('发生了一个错误，请刷新页面重试');
        }
    });
    
    // 全局未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (e) => {
        console.error('未处理的Promise拒绝:', e.reason);
        if (app) {
            app.showError('操作失败，请重试');
        }
    });
    
    // 导出到全局作用域（用于调试）
    window.app = app;
    window.dataManager = dataManager;
    
})();
