// 列表视图管理器
class ListView {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 10;
        this.activeFilters = new Set();
        this.init();
    }

    init() {
        this.bindEvents();
        this.renderFilterTags();
        this.renderCompanyList();
        this.updateResultCount();
    }

    // 绑定事件监听器
    bindEvents() {
        // 搜索输入框
        const searchInput = document.getElementById('area-search');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce((e) => {
                this.handleSearch(e.target.value);
            }, 300));
        }

        // 搜索按钮
        const searchBtn = document.querySelector('.search-btn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                const keyword = searchInput.value;
                this.handleSearch(keyword);
            });
        }

        // 筛选标签点击事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('filter-tag')) {
                this.handleFilterClick(e.target);
            }
        });

        // 清除条件
        const clearBtn = document.querySelector('.view-options');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearAllFilters();
            });
        }

        // 分页事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('page-num')) {
                const page = parseInt(e.target.textContent);
                this.goToPage(page);
            } else if (e.target.classList.contains('prev')) {
                this.goToPage(this.currentPage - 1);
            } else if (e.target.classList.contains('next')) {
                this.goToPage(this.currentPage + 1);
            }
        });
    }

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 处理搜索
    handleSearch(keyword) {
        dataManager.searchCompanies(keyword);
        this.currentPage = 1;
        this.renderCompanyList();
        this.updateResultCount();
        this.renderPagination();
    }

    // 处理筛选标签点击
    handleFilterClick(tagElement) {
        const filterType = this.getFilterType(tagElement);
        const filterValue = tagElement.textContent.trim();

        // 切换激活状态
        if (tagElement.classList.contains('active')) {
            tagElement.classList.remove('active');
            this.removeFilter(filterType);
        } else {
            // 移除同类型的其他激活标签
            this.clearFilterType(filterType);
            tagElement.classList.add('active');
            this.applyFilter(filterType, filterValue);
        }

        this.currentPage = 1;
        this.renderCompanyList();
        this.updateResultCount();
        this.renderPagination();
    }

    // 获取筛选类型
    getFilterType(tagElement) {
        const filterGroup = tagElement.closest('.filter-group');
        const label = filterGroup.querySelector('.filter-label').textContent;
        
        if (label.includes('都市')) return 'city';
        if (label.includes('区域')) return 'district';
        return 'category';
    }

    // 应用筛选
    applyFilter(type, value) {
        // 从日文标签转换为中文值
        const chineseValue = this.convertToChineseValue(type, value);
        
        switch (type) {
            case 'city':
                dataManager.filterByCity(chineseValue);
                break;
            case 'district':
                dataManager.filterByDistrict(chineseValue);
                break;
            case 'category':
                dataManager.filterByCategory(chineseValue);
                break;
        }
    }

    // 转换日文标签为中文值
    convertToChineseValue(type, japaneseValue) {
        const mappings = {
            city: {
                '杭州です': '杭州',
                '青島です': '青島',
                '上海です': '上海',
                '北京です': '北京',
                '深圳です': '深圳'
            },
            district: {
                'アップタウン': '上城区',
                '明珠地域です': '明珠地区',
                '滨水地方です': '滨江区',
                '浜江区です': '滨江区',
                '富山区です': '萧山区'
            }
        };

        return mappings[type]?.[japaneseValue] || japaneseValue;
    }

    // 移除筛选
    removeFilter(type) {
        switch (type) {
            case 'city':
                dataManager.filterByCity('');
                break;
            case 'district':
                dataManager.filterByDistrict('');
                break;
            case 'category':
                dataManager.filterByCategory('');
                break;
        }
    }

    // 清除同类型的筛选
    clearFilterType(type) {
        const filterGroup = document.querySelector(`.filter-group .filter-label:contains("${type}")`);
        if (filterGroup) {
            const tags = filterGroup.parentElement.querySelectorAll('.filter-tag');
            tags.forEach(tag => tag.classList.remove('active'));
        }
    }

    // 清除所有筛选
    clearAllFilters() {
        // 清除所有激活的标签
        document.querySelectorAll('.filter-tag.active').forEach(tag => {
            tag.classList.remove('active');
        });

        // 清除搜索输入
        const searchInput = document.getElementById('area-search');
        if (searchInput) {
            searchInput.value = '';
        }

        // 重置数据管理器
        dataManager.clearFilters();
        this.currentPage = 1;
        this.renderCompanyList();
        this.updateResultCount();
        this.renderPagination();
    }

    // 渲染筛选标签
    renderFilterTags() {
        // 城市标签
        const cityGroup = document.querySelector('.filter-group:first-child');
        if (cityGroup) {
            const existingTags = cityGroup.querySelectorAll('.filter-tag:not(:first-child)');
            existingTags.forEach(tag => tag.remove());

            cityData.forEach(city => {
                const tag = document.createElement('button');
                tag.className = 'filter-tag';
                tag.textContent = city.nameJp;
                cityGroup.appendChild(tag);
            });
        }

        // 区域标签
        const districtGroup = document.querySelector('.filter-group:last-child');
        if (districtGroup) {
            const existingTags = districtGroup.querySelectorAll('.filter-tag:not(:first-child)');
            existingTags.forEach(tag => tag.remove());

            districtData.forEach(district => {
                const tag = document.createElement('button');
                tag.className = 'filter-tag';
                tag.textContent = district.nameJp;
                districtGroup.appendChild(tag);
            });
        }
    }

    // 渲染公司列表
    renderCompanyList() {
        const container = document.getElementById('company-list');
        if (!container) return;

        const paginatedData = dataManager.getPaginatedCompanies(this.currentPage, this.pageSize);
        const companies = paginatedData.companies;

        if (companies.length === 0) {
            container.innerHTML = '<div class="no-results">没有找到符合条件的公司</div>';
            return;
        }

        container.innerHTML = companies.map(company => this.createCompanyItem(company)).join('');
    }

    // 创建公司项目HTML
    createCompanyItem(company) {
        return `
            <div class="company-item" data-company-id="${company.id}">
                <div class="company-header">
                    <div>
                        <div class="company-name">${company.name}</div>
                        <div class="company-details">
                            <div>日本社名：${company.nameJp}</div>
                        </div>
                    </div>
                    <div class="company-category">${company.category}</div>
                </div>
                <div class="company-details">
                    <div class="company-address">${company.address}</div>
                    <a href="#" class="map-link" onclick="switchToMapView(${company.id})">
                        📍 地図で位置を確認する
                    </a>
                </div>
            </div>
        `;
    }

    // 更新结果计数
    updateResultCount() {
        const countElement = document.querySelector('.result-count');
        const totalCount = dataManager.getTotalCount();
        
        if (countElement) {
            countElement.textContent = totalCount;
        }

        // 更新结果标题
        const resultsHeader = document.querySelector('.results-header h3');
        if (resultsHeader) {
            resultsHeader.innerHTML = `
                <span class="result-count">${totalCount}</span>
                社の杭州商社をお見つけしました
            `;
        }
    }

    // 渲染分页
    renderPagination() {
        const paginatedData = dataManager.getPaginatedCompanies(this.currentPage, this.pageSize);
        const { totalPages, currentPage } = paginatedData;

        const pagination = document.querySelector('.pagination');
        if (!pagination) return;

        // 更新上一页按钮
        const prevBtn = pagination.querySelector('.prev');
        if (prevBtn) {
            prevBtn.disabled = currentPage <= 1;
        }

        // 更新下一页按钮
        const nextBtn = pagination.querySelector('.next');
        if (nextBtn) {
            nextBtn.disabled = currentPage >= totalPages;
        }

        // 更新页码
        const pageNumbers = pagination.querySelector('.page-numbers');
        if (pageNumbers) {
            pageNumbers.innerHTML = this.generatePageNumbers(currentPage, totalPages);
        }
    }

    // 生成页码HTML
    generatePageNumbers(current, total) {
        let html = '';
        const maxVisible = 5;
        let start = Math.max(1, current - Math.floor(maxVisible / 2));
        let end = Math.min(total, start + maxVisible - 1);

        if (end - start + 1 < maxVisible) {
            start = Math.max(1, end - maxVisible + 1);
        }

        if (start > 1) {
            html += '<button class="page-num">1</button>';
            if (start > 2) {
                html += '<span>...</span>';
            }
        }

        for (let i = start; i <= end; i++) {
            html += `<button class="page-num ${i === current ? 'active' : ''}">${i}</button>`;
        }

        if (end < total) {
            if (end < total - 1) {
                html += '<span>...</span>';
            }
            html += `<button class="page-num">${total}</button>`;
        }

        return html;
    }

    // 跳转到指定页面
    goToPage(page) {
        const paginatedData = dataManager.getPaginatedCompanies(1, this.pageSize);
        const totalPages = paginatedData.totalPages;

        if (page < 1 || page > totalPages) return;

        this.currentPage = page;
        this.renderCompanyList();
        this.renderPagination();

        // 滚动到顶部
        document.querySelector('.results-section').scrollIntoView({ 
            behavior: 'smooth' 
        });
    }

    // 刷新视图
    refresh() {
        this.renderCompanyList();
        this.updateResultCount();
        this.renderPagination();
    }
}

// 全局函数：切换到地图视图并显示指定公司
function switchToMapView(companyId) {
    // 切换到地图视图
    document.querySelector('.nav-tab[data-view="map"]').click();
    
    // 延迟执行以确保地图视图已加载
    setTimeout(() => {
        if (window.mapView) {
            window.mapView.showCompanyDetail(companyId);
        }
    }, 500);
}

// 创建列表视图实例
let listView;
