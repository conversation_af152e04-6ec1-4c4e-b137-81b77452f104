// 模拟公司数据
const companyData = [
    {
        id: 1,
        name: "杭州现代化スパンデックス有限公司",
        nameJp: "旭化成",
        address: "中国浙江省杭州市滨江区技术开发区MTO-5-2",
        category: "化学",
        city: "杭州",
        district: "滨江区",
        coordinates: [120.2103, 30.2084],
        phone: "+86-571-12345678",
        website: "www.example.com",
        description: "专业从事化学纤维生产的现代化企业"
    },
    {
        id: 2,
        name: "東京海上日動火災保険（中国）有限公司 浙江分公司",
        nameJp: "東京海上ホールディングス",
        address: "中国浙江省杭州市上城区钱江新城1号国际财富中心1405号",
        category: "保険業",
        city: "杭州",
        district: "上城区",
        coordinates: [120.1693, 30.2467],
        phone: "+86-571-87654321",
        website: "www.tokiomarine.com",
        description: "提供全面的保险服务解决方案"
    },
    {
        id: 3,
        name: "杭州善多见贸易有限公司",
        nameJp: "株式会社オーアンドエム工芸",
        address: "中国浙江省杭州市萧山区桥南街道桥南路119号杭州湾智慧谷智慧楼10层",
        category: "貿易業",
        city: "杭州",
        district: "萧山区",
        coordinates: [120.2641, 30.1851],
        phone: "+86-571-11223344",
        website: "www.trade-company.com",
        description: "专业的国际贸易服务公司"
    },
    {
        id: 4,
        name: "三井住友銀行（中国）有限公司 杭州支店",
        nameJp: "三井住友銀行",
        address: "5F, Offices At Kerry Centre, 385 Yan An Road, Gong Shu District, Hangzhou, Zhejiang Province, The People's Republic of China",
        category: "銀行業",
        city: "杭州",
        district: "拱墅区",
        coordinates: [120.1441, 30.2875],
        phone: "+86-571-55667788",
        website: "www.smbc.co.jp",
        description: "提供专业的银行金融服务"
    },
    {
        id: 5,
        name: "杭州科技创新有限公司",
        nameJp: "テクノロジー株式会社",
        address: "中国浙江省杭州市西湖区文三路259号",
        category: "IT業",
        city: "杭州",
        district: "西湖区",
        coordinates: [120.1322, 30.2736],
        phone: "+86-571-99887766",
        website: "www.tech-innovation.com",
        description: "专注于科技创新和软件开发"
    },
    {
        id: 6,
        name: "青島製造業株式会社",
        nameJp: "青島製造業株式会社",
        address: "中国山东省青岛市市南区香港中路40号",
        category: "製造業",
        city: "青島",
        district: "市南区",
        coordinates: [120.3826, 36.0671],
        phone: "+86-532-12345678",
        website: "www.qingdao-mfg.com",
        description: "专业制造业企业，产品远销海外"
    },
    {
        id: 7,
        name: "青島国際貿易センター",
        nameJp: "青島国際貿易センター",
        address: "中国山东省青岛市崂山区海尔路1号",
        category: "貿易業",
        city: "青島",
        district: "崂山区",
        coordinates: [120.4651, 36.1052],
        phone: "+86-532-87654321",
        website: "www.qd-trade.com",
        description: "国际贸易综合服务平台"
    },
    {
        id: 8,
        name: "上海金融サービス株式会社",
        nameJp: "上海金融サービス株式会社",
        address: "中国上海市浦东新区陆家嘴环路1000号",
        category: "金融業",
        city: "上海",
        district: "浦东新区",
        coordinates: [121.5057, 31.2453],
        phone: "+86-21-12345678",
        website: "www.sh-finance.com",
        description: "提供全方位金融服务解决方案"
    },
    {
        id: 9,
        name: "北京テクノロジー開発有限公司",
        nameJp: "北京テクノロジー開発有限公司",
        address: "中国北京市朝阳区建国门外大街1号",
        category: "IT業",
        city: "北京",
        district: "朝阳区",
        coordinates: [116.4074, 39.9042],
        phone: "+86-10-12345678",
        website: "www.bj-tech.com",
        description: "领先的技术开发和创新企业"
    },
    {
        id: 10,
        name: "深圳電子工業株式会社",
        nameJp: "深圳電子工業株式会社",
        address: "中国广东省深圳市南山区科技园南区",
        category: "電子業",
        city: "深圳",
        district: "南山区",
        coordinates: [113.9547, 22.5431],
        phone: "+86-755-12345678",
        website: "www.sz-electronics.com",
        description: "专业电子产品制造和研发"
    }
];

// 城市数据
const cityData = [
    { name: "杭州", nameJp: "杭州です", count: 5 },
    { name: "青島", nameJp: "青島です", count: 2 },
    { name: "上海", nameJp: "上海です", count: 1 },
    { name: "北京", nameJp: "北京です", count: 1 },
    { name: "深圳", nameJp: "深圳です", count: 1 }
];

// 区域数据
const districtData = [
    { name: "滨江区", nameJp: "滨江区です", city: "杭州", count: 1 },
    { name: "上城区", nameJp: "上城区です", city: "杭州", count: 1 },
    { name: "萧山区", nameJp: "萧山区です", city: "杭州", count: 1 },
    { name: "拱墅区", nameJp: "拱墅区です", city: "杭州", count: 1 },
    { name: "西湖区", nameJp: "西湖区です", city: "杭州", count: 1 },
    { name: "市南区", nameJp: "市南区です", city: "青島", count: 1 },
    { name: "崂山区", nameJp: "崂山区です", city: "青島", count: 1 },
    { name: "浦东新区", nameJp: "浦东新区です", city: "上海", count: 1 },
    { name: "朝阳区", nameJp: "朝阳区です", city: "北京", count: 1 },
    { name: "南山区", nameJp: "南山区です", city: "深圳", count: 1 }
];

// 行业分类数据
const categoryData = [
    { name: "化学", nameJp: "化学", count: 1 },
    { name: "保険業", nameJp: "保険業", count: 1 },
    { name: "貿易業", nameJp: "貿易業", count: 2 },
    { name: "銀行業", nameJp: "銀行業", count: 1 },
    { name: "IT業", nameJp: "IT業", count: 2 },
    { name: "製造業", nameJp: "製造業", count: 1 },
    { name: "金融業", nameJp: "金融業", count: 1 },
    { name: "電子業", nameJp: "電子業", count: 1 }
];

// 搜索和筛选功能
class DataManager {
    constructor() {
        this.allCompanies = [...companyData];
        this.filteredCompanies = [...companyData];
        this.currentFilters = {
            keyword: '',
            city: '',
            district: '',
            category: ''
        };
    }

    // 搜索公司
    searchCompanies(keyword) {
        this.currentFilters.keyword = keyword.toLowerCase();
        this.applyFilters();
        return this.filteredCompanies;
    }

    // 按城市筛选
    filterByCity(city) {
        this.currentFilters.city = city;
        this.applyFilters();
        return this.filteredCompanies;
    }

    // 按区域筛选
    filterByDistrict(district) {
        this.currentFilters.district = district;
        this.applyFilters();
        return this.filteredCompanies;
    }

    // 按行业筛选
    filterByCategory(category) {
        this.currentFilters.category = category;
        this.applyFilters();
        return this.filteredCompanies;
    }

    // 应用所有筛选条件
    applyFilters() {
        this.filteredCompanies = this.allCompanies.filter(company => {
            const matchesKeyword = !this.currentFilters.keyword || 
                company.name.toLowerCase().includes(this.currentFilters.keyword) ||
                company.nameJp.toLowerCase().includes(this.currentFilters.keyword) ||
                company.address.toLowerCase().includes(this.currentFilters.keyword);

            const matchesCity = !this.currentFilters.city || 
                company.city === this.currentFilters.city;

            const matchesDistrict = !this.currentFilters.district || 
                company.district === this.currentFilters.district;

            const matchesCategory = !this.currentFilters.category || 
                company.category === this.currentFilters.category;

            return matchesKeyword && matchesCity && matchesDistrict && matchesCategory;
        });
    }

    // 清除所有筛选条件
    clearFilters() {
        this.currentFilters = {
            keyword: '',
            city: '',
            district: '',
            category: ''
        };
        this.filteredCompanies = [...this.allCompanies];
        return this.filteredCompanies;
    }

    // 获取当前筛选结果
    getFilteredCompanies() {
        return this.filteredCompanies;
    }

    // 获取公司总数
    getTotalCount() {
        return this.filteredCompanies.length;
    }

    // 根据ID获取公司详情
    getCompanyById(id) {
        return this.allCompanies.find(company => company.id === id);
    }

    // 获取分页数据
    getPaginatedCompanies(page = 1, pageSize = 10) {
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        return {
            companies: this.filteredCompanies.slice(startIndex, endIndex),
            totalPages: Math.ceil(this.filteredCompanies.length / pageSize),
            currentPage: page,
            totalCount: this.filteredCompanies.length
        };
    }
}

// 创建全局数据管理器实例
const dataManager = new DataManager();
