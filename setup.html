<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公司检索系统 - 配置向导</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        
        .setup-container {
            background: white;
            border-radius: 16px;
            padding: 3rem;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .setup-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .setup-header h1 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .setup-header p {
            color: #7f8c8d;
            font-size: 1.1rem;
        }
        
        .step {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            transition: border-color 0.3s ease;
        }
        
        .step:hover {
            border-color: #667eea;
        }
        
        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
        }
        
        .step-title {
            color: #2c3e50;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }
        
        .step-content {
            color: #666;
            line-height: 1.6;
        }
        
        .api-key-input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            margin: 1rem 0;
            transition: border-color 0.3s ease;
        }
        
        .api-key-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #e9ecef;
        }
        
        .btn-secondary:hover {
            background: #e9ecef;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 1rem 0;
            overflow-x: auto;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
            display: none;
        }
        
        .warning-message {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .actions {
            text-align: center;
            margin-top: 2rem;
            display: flex;
            gap: 1rem;
            justify-content: center;
        }
        
        @media (max-width: 768px) {
            .setup-container {
                padding: 2rem;
                margin: 1rem;
            }
            
            .setup-header h1 {
                font-size: 1.5rem;
            }
            
            .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1>🏢 公司检索系统</h1>
            <p>配置向导 - 快速开始使用</p>
        </div>
        
        <div class="step">
            <div class="step-title">
                <span class="step-number">1</span>
                获取高德地图API密钥
            </div>
            <div class="step-content">
                <p>访问 <a href="https://lbs.amap.com/" target="_blank" style="color: #667eea;">高德开放平台</a> 并完成以下步骤：</p>
                <ul style="margin: 1rem 0; padding-left: 2rem;">
                    <li>注册账号并登录</li>
                    <li>进入控制台，创建新应用</li>
                    <li>选择"Web服务"平台</li>
                    <li>复制生成的API Key</li>
                </ul>
            </div>
        </div>
        
        <div class="step">
            <div class="step-title">
                <span class="step-number">2</span>
                配置API密钥
            </div>
            <div class="step-content">
                <p>将您的API密钥粘贴到下面的输入框中：</p>
                <input type="text" id="apiKeyInput" class="api-key-input" placeholder="请输入您的高德地图API密钥">
                
                <div class="warning-message">
                    <strong>注意：</strong> API密钥将保存在本地浏览器中，不会上传到任何服务器。
                </div>
                
                <div id="successMessage" class="success-message">
                    <strong>配置成功！</strong> API密钥已保存，您现在可以正常使用系统了。
                </div>
            </div>
        </div>
        
        <div class="step">
            <div class="step-title">
                <span class="step-number">3</span>
                启动本地服务器
            </div>
            <div class="step-content">
                <p>由于使用了现代Web API，需要通过HTTP服务器访问。选择以下任一方式：</p>
                
                <div class="code-block">
# 使用Python<br>
python -m http.server 8000<br><br>
# 使用Node.js<br>
npx http-server<br><br>
# 使用PHP<br>
php -S localhost:8000
                </div>
                
                <p>然后在浏览器中访问 <code>http://localhost:8000</code></p>
            </div>
        </div>
        
        <div class="actions">
            <button class="btn" onclick="saveApiKey()">保存配置</button>
            <a href="index.html" class="btn btn-secondary">进入系统</a>
        </div>
    </div>
    
    <script>
        function saveApiKey() {
            const apiKey = document.getElementById('apiKeyInput').value.trim();
            
            if (!apiKey) {
                alert('请输入API密钥');
                return;
            }
            
            // 保存到localStorage
            localStorage.setItem('amap_api_key', apiKey);
            
            // 显示成功消息
            document.getElementById('successMessage').style.display = 'block';
            
            // 3秒后自动跳转
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 3000);
        }
        
        // 页面加载时检查是否已有API密钥
        window.addEventListener('load', () => {
            const savedKey = localStorage.getItem('amap_api_key');
            if (savedKey) {
                document.getElementById('apiKeyInput').value = savedKey;
            }
        });
    </script>
</body>
</html>
