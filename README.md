# 公司检索系统

一个现代化的公司检索系统，提供传统列表检索和高德地图集成两种查看模式。

## 功能特性

### 🔍 传统检索列表页面
- **智能搜索**: 支持公司名称、地址、日文名称的模糊搜索
- **多维筛选**: 按城市、区域、行业类别进行筛选
- **分页显示**: 优化的分页组件，支持大量数据展示
- **响应式设计**: 完美适配桌面、平板、手机等各种设备

### 🗺️ 高德地图集成页面
- **地图标点**: 将公司位置以标点形式显示在高德地图上
- **智能聚合**: 根据地图缩放级别和区域自动聚合显示
- **交互详情**: 点击标点查看公司详细信息
- **路线导航**: 集成高德地图导航功能
- **位置分享**: 支持位置信息分享和复制

### 🎨 用户体验
- **流畅切换**: 两种视图模式间的无缝切换
- **数据同步**: 搜索和筛选条件在不同视图间保持同步
- **URL状态**: 支持浏览器前进后退，可分享特定搜索状态的链接
- **加载优化**: 智能的资源加载和缓存策略

## 技术栈

- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **地图服务**: 高德地图 JavaScript API
- **UI设计**: 现代化的渐变设计，Material Design风格
- **响应式**: CSS Grid, Flexbox, Media Queries
- **架构**: 模块化设计，事件驱动架构

## 快速开始

### 1. 获取高德地图API密钥

1. 访问 [高德开放平台](https://lbs.amap.com/)
2. 注册账号并创建应用
3. 获取 Web服务 API Key

### 2. 配置API密钥

#### 方法一：使用配置向导（推荐）
1. 在浏览器中打开 `setup.html`
2. 按照向导步骤获取并配置API密钥
3. 系统会自动保存配置到本地存储

#### 方法二：手动编辑配置文件
编辑 `js/config.js` 文件，将您的API密钥替换到相应位置：

```javascript
const CONFIG = {
    AMAP: {
        KEY: 'YOUR_AMAP_KEY_HERE', // 替换为您的API密钥
        // ... 其他配置
    }
};
```

### 3. 部署运行

#### 方法一：本地开发服务器
```bash
# 使用Python启动本地服务器
python -m http.server 8000

# 或使用Node.js
npx http-server

# 或使用PHP
php -S localhost:8000
```

#### 方法二：直接打开文件
由于使用了ES6模块和某些Web API，建议通过HTTP服务器访问，而不是直接打开HTML文件。

### 4. 访问应用

打开浏览器访问 `http://localhost:8000`

## 项目结构

```
cocos-map/
├── index.html              # 主页面
├── css/
│   └── style.css          # 样式文件
├── js/
│   ├── config.js          # 配置文件
│   ├── data.js            # 数据管理
│   ├── utils.js           # 工具函数
│   ├── list-view.js       # 列表视图
│   ├── map-view.js        # 地图视图
│   └── app.js             # 主应用程序
└── README.md              # 说明文档
```

## 配置说明

### 地图配置
```javascript
AMAP: {
    KEY: 'YOUR_AMAP_KEY_HERE',           // API密钥
    DEFAULT_CENTER: [120.1693, 30.2467], // 默认中心点（杭州）
    DEFAULT_ZOOM: 10,                     // 默认缩放级别
    MAP_STYLE: 'amap://styles/normal'     // 地图样式
}
```

### 聚合配置
```javascript
CLUSTER: {
    GRID_SIZE: {
        ZOOM_8: 120,    // 缩放级别8时的网格大小
        ZOOM_10: 100,   // 缩放级别10时的网格大小
        ZOOM_12: 80,    // 缩放级别12时的网格大小
        ZOOM_14: 60,    // 缩放级别14时的网格大小
        DEFAULT: 40     // 默认网格大小
    },
    MAX_ZOOM: 15        // 最大聚合缩放级别
}
```

### 分页配置
```javascript
PAGINATION: {
    PAGE_SIZE: 10,           // 每页显示数量
    MAX_VISIBLE_PAGES: 5     // 最大可见页码数
}
```

## 数据格式

### 公司数据结构
```javascript
{
    id: 1,                                    // 唯一标识
    name: "杭州现代化スパンデックス有限公司",      // 公司名称
    nameJp: "旭化成",                         // 日文名称
    address: "中国浙江省杭州市滨江区...",        // 地址
    category: "化学",                         // 行业类别
    city: "杭州",                            // 城市
    district: "滨江区",                       // 区域
    coordinates: [120.2103, 30.2084],        // 经纬度坐标
    phone: "+86-571-12345678",               // 电话
    website: "www.example.com",              // 网站
    description: "专业从事化学纤维生产..."      // 描述
}
```

## 自定义开发

### 添加新的公司数据
编辑 `js/data.js` 文件中的 `companyData` 数组：

```javascript
const companyData = [
    {
        id: 11,
        name: "新公司名称",
        nameJp: "新会社名",
        address: "公司地址",
        category: "行业类别",
        city: "城市",
        district: "区域",
        coordinates: [经度, 纬度],
        // ... 其他字段
    },
    // ... 现有数据
];
```

### 自定义样式
编辑 `css/style.css` 文件来修改界面样式：

- 修改颜色主题
- 调整布局间距
- 自定义响应式断点
- 添加动画效果

### 扩展功能
- 在 `js/utils.js` 中添加工具函数
- 在 `js/config.js` 中添加新的配置项
- 修改 `js/app.js` 来添加新的应用逻辑

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 注意事项

1. **API密钥安全**: 请妥善保管您的高德地图API密钥，避免在公开代码库中暴露
2. **HTTPS要求**: 地理定位功能需要在HTTPS环境下使用
3. **跨域问题**: 如果需要从外部API获取数据，请注意跨域配置
4. **性能优化**: 大量数据时建议实现服务端分页和搜索

## 许可证

MIT License

## 支持

如有问题或建议，请提交Issue或联系开发团队。
